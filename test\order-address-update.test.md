# 订单修改服务地址接口测试

## 接口信息
- **接口地址**: `PUT /orders/{orderId}/updateServiceAddress`
- **接口描述**: 修改订单的服务地址信息

## 测试用例

### 1. 管理端修改地址（无状态限制）

**请求示例**:
```bash
curl -X PUT "http://localhost:7001/orders/1/updateServiceAddress" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "北京市朝阳区新地址",
    "addressDetail": "新小区2号楼202室",
    "longitude": 116.456789,
    "latitude": 39.987654,
    "addressRemark": "新地址备注信息",
    "userType": "admin"
  }'
```

**预期响应**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2. 员工端修改地址（有状态限制）

**请求示例**:
```bash
curl -X PUT "http://localhost:7001/orders/1/updateServiceAddress" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "北京市朝阳区员工修改地址",
    "addressDetail": "员工修改小区3号楼303室",
    "longitude": 116.111111,
    "latitude": 39.222222,
    "employeeId": 1,
    "userType": "employee"
  }'
```

**预期响应（订单状态允许时）**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**预期响应（订单状态不允许时）**:
```json
{
  "errCode": 400,
  "msg": "当前订单状态不允许修改服务地址"
}
```

### 3. 用户端修改地址（有状态限制）

**请求示例**:
```bash
curl -X PUT "http://localhost:7001/orders/1/updateServiceAddress" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "北京市朝阳区用户修改地址",
    "addressDetail": "用户修改小区4号楼404室",
    "longitude": 116.333333,
    "latitude": 39.444444,
    "addressId": 5,
    "userType": "customer"
  }'
```

### 4. 部分字段更新

**请求示例**:
```bash
curl -X PUT "http://localhost:7001/orders/1/updateServiceAddress" \
  -H "Content-Type: application/json" \
  -d '{
    "addressRemark": "只修改备注信息",
    "userType": "admin"
  }'
```

## 状态限制说明

### 允许修改的状态（员工端/用户端）
- 待付款
- 待接单
- 待服务

### 不允许修改的状态（员工端/用户端）
- 已出发
- 服务中
- 已完成
- 已评价
- 已取消
- 退款中
- 已退款

### 管理端
- 所有状态均可修改

## 验证要点

1. **权限验证**: 不同用户类型的状态限制是否正确
2. **数据验证**: 经纬度范围、字符串长度限制
3. **日志记录**: 操作是否正确记录到服务变更日志
4. **字段更新**: 只更新提供的字段，未提供的字段保持不变
5. **关联地址**: addressId字段是否正确关联到客户地址表
